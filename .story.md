---
focus: src/person/PersonController.js
---
### Single reason to change

Main idea that SRP guards is a single reason to change. It concludes that a class or method should be focused on the one thing
and have one, and only one, reason to be changed.

Please take a look at [PersonController.js](src/person/PersonController.js)
class. It keeps almost whole project business logic and is responsible of request controlling, validation and data storing operations.
Let's try to refactor it step by step to make it more suitable for further development and support.
