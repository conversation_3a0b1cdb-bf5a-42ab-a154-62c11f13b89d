{"name": "task2.4.1-javascript-refactoring-tamplate", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "babel src/ -d dist/", "start": "babel-node src/index.js", "test": "jest --ci --reporters=default --reporters=jest-junit"}, "repository": {"type": "git", "url": "https://git.epam.com/Refactoring-Stories/single-responsibility-principle-story-js.git"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@babel/cli": "^7.23.4", "@babel/core": "^7.23.3", "@babel/node": "^7.6.2", "@babel/plugin-proposal-class-properties": "^7.18.6", "@babel/preset-env": "^7.6.2", "nodemon": "^3.0.1", "jest": "^29.7.0", "jest-junit": "^16.0.0"}, "jest": {"bail": true, "testMatch": ["**/tests/**/*.test.js"], "reporters": [["jest-junit", {"outputDirectory": "test-results", "outputName": "junit.xml"}]]}, "dependencies": {"body-parser": "^1.19.0", "cors": "^2.8.5", "dotenv": "^8.1.0", "express": "^4.17.1", "http-status-codes": "^1.3.2", "nodemailer": "^6.3.0", "uuid": "^7.0.3"}}