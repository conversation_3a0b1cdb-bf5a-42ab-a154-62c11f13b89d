<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="jest tests" tests="8" failures="0" errors="0" time="1.273">
  <testsuite name="PersonService" errors="0" failures="0" skipped="0" timestamp="2025-06-17T13:24:33" time="1.156" tests="8">
    <testcase classname="PersonService has method findPersonByEmail" name="PersonService has method findPersonByEmail" time="0.001">
    </testcase>
    <testcase classname="PersonService has method store" name="PersonService has method store" time="0.001">
    </testcase>
    <testcase classname="ValidationService has method validatePerson" name="ValidationService has method validatePerson" time="0">
    </testcase>
    <testcase classname="ValidationService has method validateEmail" name="ValidationService has method validateEmail" time="0">
    </testcase>
    <testcase classname="ValidationService has method validateContact" name="ValidationService has method validateContact" time="0.001">
    </testcase>
    <testcase classname="Person<PERSON>ontroller has method findByEmail" name="PersonController has method findByEmail" time="0.001">
    </testcase>
    <testcase classname="PersonController has method storePerson" name="PersonController has method storePerson" time="0">
    </testcase>
    <testcase classname="PersonController has no method isValidEmail" name="PersonController has no method isValidEmail" time="0.001">
    </testcase>
  </testsuite>
</testsuites>