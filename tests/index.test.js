import PersonController from '../src/person/PersonController';


describe('PersonService', () => {
  let PersonService;
  let personService;
  let emailService;

  beforeAll(async () => {
    try {
      PersonService = (await import('../src/person/PersonService')).default;
      emailService = { findPersonByEmail: jest.fn(), store: jest.fn() };
      personService = new PersonService(emailService);
    } catch (e) {
      throw new Error('PersonService class does not exist');
    }
  });

  it('has method findPersonByEmail', () => {
    expect(personService.findPersonByEmail).toBeDefined();
  });

  it('has method store', () => {
    expect(personService.store).toBeDefined();
  });
});

describe('ValidationService', () => {
  let ValidationService;
  let validationService;

  beforeAll(async () => {
    try {
      ValidationService = (await import('../src/person/ValidationService')).default;
      validationService = new ValidationService();
    } catch (e) {
      throw new Error('ValidationService class does not exist');
    }
  });

  it('has method validatePerson', () => {
    expect(validationService.validatePerson).toBeDefined();
  });

  it('has method validateEmail', () => {
    expect(validationService.validateEmail).toBeDefined();
  });

  it('has method validateContact', () => {
    expect(validationService.validateContact).toBeDefined();
  });
});

describe('PersonController', () => {
    let personService;

    beforeEach(() => {
        personService = {
            findPersonByEmail: jest.fn(),
            store: jest.fn(),
        };
    });

    it('has method findByEmail', () => {
        const controller = new PersonController(personService);
        expect(controller.findByEmail).toBeDefined();
    });

    it('has method storePerson', () => {
        const controller = new PersonController(personService);
        expect(controller.storePerson).toBeDefined();
    });

    it('has no method isValidEmail', () => {
        const controller = new PersonController(personService);
        expect(controller.isValidEmail).toBeUndefined();
    });
});

