import { Router } from "express";
import PersonController from "../person/PersonController";
import ValidationService from "../person/ValidationService";
import EmailService from "../person/EmailService";
import PersonService from "../person/PersonService";

const validationService = new ValidationService();
const emailService = new EmailService();
const personService = new PersonService(emailService);
const controller = new PersonController(personService, validationService);
const router = Router();

router.get("/contact", controller.findByEmail);
router.post("/contact", controller.storePerson);

export default router;
