import nodemailer from "nodemailer";
import { Contact } from "./Contact.js";
import logger from "../utils/logger.js";

export default class EmailService {
    constructor() {
        this.ownerName = process.env.OWNER_NAME;
        this.ownerEmail = process.env.OWNER_EMAIL;
        this.emailSubject = process.env.EMAIL_SUBJECT;
    }

    greet<PERSON><PERSON>(person) {
        const recipient = person.contacts.find(
            (contact) => contact.type === Contact.EMAIL_CONTACT_TYPE
        );
        try {
            const body = this.prepareEmailBody(person);
            this.sendEmail(
                recipient,
                this.ownerEmail,
                this.emailSubject,
                body
            ).catch((error) => logger.error("Email sending error: ", error));
        } catch (error) {
            logger.error("Email sending error: ", error);
        }
    }

    sendEmail(from, to, subject, body) {
        const transporter = nodemailer.createTransport({
            host: process.env.MAIL_HOST,
            port: process.env.MAIL_PORT,
            secure: false,
            auth: {
                user: process.env.MAIL_USERNAME,
                pass: process.env.MAIL_PASSWORD,
            },
        });

        return transporter.sendMail({
            from,
            to,
            subject,
            text: body,
        });
    }

    prepareEmailBody(person) {
        return `
            <!DOCTYPE html>
            <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <title>Greeting</title>
                </head>
                <body>
                    Hello ${person.firstName} ${person.lastName}!
                    You've been added to ${this.ownerName} contacts.
                </body>
            </html>`;
    }
}
