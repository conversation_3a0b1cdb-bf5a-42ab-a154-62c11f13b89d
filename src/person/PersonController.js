import * as HttpStatus from "http-status-codes";
import { Person } from "./Person";

class PersonController {
    constructor(personService, validationService) {
        this.personService = personService;
        this.validationService = validationService;
    }

    findByEmail = (req, res) => {
        const email = req.query["email"];
        if (this.validationService.validateEmail(email)) {
            res.status(HttpStatus.OK).json(
                this.personService.findPersonByEmail(email)
            );
        } else {
            res.status(HttpStatus.BAD_REQUEST).json({
                message: "Email is required",
                details: "email field is empty or invalid",
            });
        }
    };

    storePerson = (req, res) => {
        const person = new Person(
            req.body.firstName,
            req.body.lastName,
            req.body.birthday,
            req.body.addresses,
            req.body.phones,
            req.body.contacts
        );
        if (this.validationService.validate<PERSON>erson(person)) {
            this.personService.store(person);
            res.sendStatus(HttpStatus.OK);
        } else {
            res.status(HttpStatus.BAD_REQUEST).json({
                message: "Email is required",
                details: "contact has no any valid emails",
            });
        }
    };
}

export default PersonController;
