import {Contact} from "./Contact.js";

export default class PersonService {
    constructor(emailService) {
        this.persons = [];
        this.emailService = emailService;
    }

    store(person) {
        this.persons.push(person);
        if (this.emailService) {
            this.emailService.greetPerson(person);
        }
    }

    findPersonByEmail(email) {
        return this.persons.find(
            person => person.contacts.some(contact =>
                contact.type === Contact.EMAIL_CONTACT_TYPE &&
                contact.contact === email
            )
        );
    }
}
