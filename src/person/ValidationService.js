import {Contact} from "./Contact.js";

export default class ValidationService {
    EMAIL_PATTERN = new RegExp('^(([^<>()\\[\\]\\\\.,;:\\s@"]+(\\.[^<>()\\[\\]\\\\.,;:\\s@"]+)*)|(".+"))@((\\[[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}\\.[0-9]{1,3}])|(([a-zA-Z\\-0-9]+\\.)+[a-zA-Z]{2,}))$');

    validateEmail(email) {
        return email && this.EMAIL_PATTERN.test(email);
    }

    validateContact(contact) {
        return contact.type === Contact.EMAIL_CONTACT_TYPE && 
               this.validateEmail(contact.contact);
    }

    validatePerson(person) {
        return person.contacts.some(contact => this.validateContact(contact));
    }
}
